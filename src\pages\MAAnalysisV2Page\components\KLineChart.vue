<template>
  <el-card class="k-line-chart-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>K线图与多期间买卖点</h3>
        <div class="legend-info">
          <span class="legend-item">
            <span class="legend-symbol buy-symbol">↑</span> 买入点
          </span>
          <span class="legend-item">
            <span class="legend-symbol sell-symbol">↓</span> 卖出点
          </span>
          <span class="legend-item">
            <span class="legend-symbol failure-symbol">▼</span> 三阶段投资失败
          </span>
        </div>
        <div class="period-controls">
          <el-checkbox-group v-model="visiblePeriods" @change="updateChart">
            <el-checkbox
              v-for="result in simulationResults"
              :key="result.period"
              :label="result.period"
              :value="result.period"
            >
              MA{{ result.period }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </template>
    <div ref="chartRef" style="width: 100%; height: 600px;"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, computed } from 'vue';
import * as echarts from 'echarts';
import { getStockData } from '@/api/stock'; // 假设通用K线数据API函数

const props = defineProps({
  simulationResults: {
    type: Array,
    required: true,
  },
  stockCode: {
    type: String,
    required: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  }
});

const chartRef = ref(null);
let chartInstance = null;
const klineData = ref([]);
const visiblePeriods = ref([]);

// 获取颜色
const getLineColor = (index) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#C71585', '#FF8C00', '#32CD32'];
  return colors[index % colors.length];
};

const prepareChartData = () => {
  console.log('准备图表数据，当前可见周期:', visiblePeriods.value);
  const dates = klineData.value.map(item => item.trade_date);
  const ohlc = klineData.value.map(item => [item.open, item.close, item.low, item.high]);

  const buyPointsSeries = [];
  const sellPointsSeries = [];
  const failurePointsSeries = [];

  // 为每个可见的周期创建买卖点系列和投资失败标记系列
  props.simulationResults.forEach((result, index) => {
    if (visiblePeriods.value.includes(result.period)) {
      const buyPoints = result.transactions
        .filter(t => t.action === 'buy')
        .map(t => [t.trade_date, t.price]);

      const sellPoints = result.transactions
        .filter(t => t.action === 'sell')
        .map(t => [t.trade_date, t.price]);

      // 提取投资失败的交易日
      const failurePoints = [];
      if (result.daily_logs && Array.isArray(result.daily_logs)) {
        result.daily_logs.forEach(log => {
          if (log.is_investment_failed === true) {
            // 获取当日的收盘价作为标记位置
            const dayKlineData = klineData.value.find(k => k.trade_date === log.date);
            if (dayKlineData) {
              failurePoints.push([log.date, dayKlineData.close]);
            }
          }
        });
      }

      if (buyPoints.length > 0) {
        buyPointsSeries.push({
          name: `MA${result.period}买入`,
          type: 'scatter',
          symbol: 'arrow',
          symbolSize: 12,
          symbolRotate: 180,
          data: buyPoints,
          itemStyle: { color: getLineColor(index) },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: getLineColor(index)
            }
          }
        });
      }

      if (sellPoints.length > 0) {
        sellPointsSeries.push({
          name: `MA${result.period}卖出`,
          type: 'scatter',
          symbol: 'arrow',
          symbolSize: 12,
          data: sellPoints,
          itemStyle: { color: getLineColor(index) },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: getLineColor(index)
            }
          }
        });
      }

      // 添加投资失败标记系列
      if (failurePoints.length > 0) {
        failurePointsSeries.push({
          name: `MA${result.period}投资失败`,
          type: 'scatter',
          symbol: 'triangle', // 使用三角形符号，更醒目
          symbolSize: 18,
          symbolRotate: 180, // 倒三角形，表示警告
          data: failurePoints,
          itemStyle: {
            color: '#FF3333',
            borderColor: '#990000',
            borderWidth: 3
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 20,
              shadowColor: '#FF3333',
              scale: 1.5
            }
          },
          z: 15 // 确保失败标记显示在最上层
        });
      }
    }
  });

  return { dates, ohlc, buyPointsSeries, sellPointsSeries, failurePointsSeries };
};

const renderChart = () => {
  if (!chartInstance || klineData.value.length === 0) return;
  const { dates, ohlc, buyPointsSeries, sellPointsSeries, failurePointsSeries } = prepareChartData();

  const legendData = ['K线'];
  buyPointsSeries.forEach(series => legendData.push(series.name));
  sellPointsSeries.forEach(series => legendData.push(series.name));
  failurePointsSeries.forEach(series => legendData.push(series.name));

  // 清除之前的图表配置，确保完全重新渲染
  chartInstance.clear();

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          if (param.seriesType === 'candlestick') {
            const data = param.data;
            result += `<div>开盘: ${data[1]}</div>`;
            result += `<div>收盘: ${data[2]}</div>`;
            result += `<div>最低: ${data[3]}</div>`;
            result += `<div>最高: ${data[4]}</div>`;
          } else if (param.seriesType === 'scatter') {
            if (param.seriesName.includes('投资失败')) {
              result += `<div style="color: ${param.color}; font-weight: bold;">⚠️ ${param.seriesName}: 三阶段投资失败</div>`;
            } else {
              result += `<div style="color: ${param.color};">${param.seriesName}: ${param.data[1]}</div>`;
            }
          }
        });
        return result;
      }
    },
    legend: {
      data: legendData,
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      scale: true,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: '{value}'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        start: 70,
        end: 100,
        height: 30
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: ohlc,
        itemStyle: {
          color: '#ef232a',
          color0: '#14b143',
          borderColor: '#ef232a',
          borderColor0: '#14b143'
        }
      },
      ...buyPointsSeries,
      ...sellPointsSeries,
      ...failurePointsSeries
    ]
  };

  chartInstance.setOption(option, true); // 第二个参数为true，表示不合并配置，完全替换
};

const updateChart = () => {
  console.log('更新图表，当前可见周期:', visiblePeriods.value);
  renderChart();
};

const fetchKlineData = async () => {
  try {
    klineData.value = await getStockData(props.stockCode, props.startDate, props.endDate);
    renderChart();
  } catch (error) {
    console.error("获取K线数据失败:", error);
  }
};

// 初始化可见周期（默认显示前3个周期）
const initVisiblePeriods = () => {
  if (props.simulationResults && props.simulationResults.length > 0) {
    visiblePeriods.value = props.simulationResults
      .slice(0, Math.min(3, props.simulationResults.length))
      .map(result => result.period);
  }
};

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    initVisiblePeriods();
    fetchKlineData();
  }
});

watch(() => props.simulationResults, () => {
  initVisiblePeriods();
  fetchKlineData();
}, { deep: true });
</script>

<style scoped>
.k-line-chart-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-symbol {
  font-weight: bold;
  font-size: 14px;
}

.buy-symbol {
  color: #67C23A;
}

.sell-symbol {
  color: #409EFF;
}

.failure-symbol {
  color: #FF3333;
}

.period-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.period-controls :deep(.el-checkbox) {
  margin-right: 15px;
}

.period-controls :deep(.el-checkbox__label) {
  font-size: 12px;
}
</style>