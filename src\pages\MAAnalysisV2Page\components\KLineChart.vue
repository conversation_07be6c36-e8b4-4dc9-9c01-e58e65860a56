<template>
  <el-card class="k-line-chart-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>K线图与多期间买卖点</h3>
        <div class="legend-info">
          <span class="legend-item">
            <span class="legend-symbol ma-symbol">—</span> 均线 (绿色线条)
          </span>
          <span class="legend-item">
            <span class="legend-symbol buy-symbol">●</span> 买入点 (蓝色圆形)
          </span>
          <span class="legend-item">
            <span class="legend-symbol sell-symbol">◆</span> 卖出点 (橙色菱形)
          </span>
          <span class="legend-item">
            <span class="legend-symbol failure-symbol">■</span> 三阶段投资失败 (紫色方形)
          </span>
        </div>
        <div class="period-controls">
          <el-checkbox-group v-model="visiblePeriods" @change="updateChart">
            <el-checkbox
              v-for="result in simulationResults"
              :key="result.period"
              :label="result.period"
              :value="result.period"
            >
              MA{{ result.period }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </template>
    <div ref="chartRef" style="width: 100%; height: 600px;"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, computed } from 'vue';
import * as echarts from 'echarts';
import { getStockData } from '@/api/stock'; // 假设通用K线数据API函数

const props = defineProps({
  simulationResults: {
    type: Array,
    required: true,
  },
  stockCode: {
    type: String,
    required: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  }
});

const chartRef = ref(null);
let chartInstance = null;
const klineData = ref([]);
const visiblePeriods = ref([]);

// 获取买入点颜色 - 使用蓝色系
const getBuyColor = (index) => {
  const colors = ['#1890FF', '#096DD9', '#0050B3', '#003A8C', '#002766', '#40A9FF', '#69C0FF', '#91D5FF'];
  return colors[index % colors.length];
};

// 获取卖出点颜色 - 使用橙色系
const getSellColor = (index) => {
  const colors = ['#FA8C16', '#D46B08', '#AD4E00', '#873800', '#612500', '#FFA940', '#FFC069', '#FFD591'];
  return colors[index % colors.length];
};

// 获取投资失败标记颜色 - 使用紫色系
const getFailureColor = (index) => {
  const colors = ['#722ED1', '#531DAB', '#391085', '#22075E', '#120338', '#9254DE', '#B37FEB', '#D3ADF7'];
  return colors[index % colors.length];
};

// 获取均线颜色 - 使用绿色系
const getMALineColor = (index) => {
  const colors = ['#52C41A', '#389E0D', '#237804', '#135200', '#092B00', '#73D13D', '#95DE64', '#B7EB8F'];
  return colors[index % colors.length];
};

const prepareChartData = () => {
  console.log('准备图表数据，当前可见周期:', visiblePeriods.value);
  const dates = klineData.value.map(item => item.trade_date);
  const ohlc = klineData.value.map(item => [item.open, item.close, item.low, item.high]);

  const buyPointsSeries = [];
  const sellPointsSeries = [];
  const failurePointsSeries = [];
  const maSeries = [];

  // 为每个可见的周期创建买卖点系列和投资失败标记系列
  props.simulationResults.forEach((result, index) => {
    if (visiblePeriods.value.includes(result.period)) {
      const buyPoints = result.transactions
        .filter(t => t.action === 'buy')
        .map(t => [t.trade_date, t.price]);

      const sellPoints = result.transactions
        .filter(t => t.action === 'sell')
        .map(t => [t.trade_date, t.price]);

      // 提取投资失败的交易日
      const failurePoints = [];
      if (result.daily_logs && Array.isArray(result.daily_logs)) {
        result.daily_logs.forEach(log => {
          if (log.is_investment_failed === true) {
            // 获取当日的收盘价作为标记位置
            const dayKlineData = klineData.value.find(k => k.trade_date === log.date);
            if (dayKlineData) {
              failurePoints.push([log.date, dayKlineData.close]);
            }
          }
        });
      }

      if (buyPoints.length > 0) {
        buyPointsSeries.push({
          name: `MA${result.period}买入`,
          type: 'scatter',
          symbol: 'circle', // 使用圆形符号
          symbolSize: 14,
          data: buyPoints,
          itemStyle: {
            color: getBuyColor(index),
            borderColor: '#FFFFFF',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 12,
              shadowColor: getBuyColor(index),
              scale: 1.3
            }
          }
        });
      }

      if (sellPoints.length > 0) {
        sellPointsSeries.push({
          name: `MA${result.period}卖出`,
          type: 'scatter',
          symbol: 'diamond', // 使用菱形符号
          symbolSize: 14,
          data: sellPoints,
          itemStyle: {
            color: getSellColor(index),
            borderColor: '#FFFFFF',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 12,
              shadowColor: getSellColor(index),
              scale: 1.3
            }
          }
        });
      }

      // 添加投资失败标记系列
      if (failurePoints.length > 0) {
        failurePointsSeries.push({
          name: `MA${result.period}投资失败`,
          type: 'scatter',
          symbol: 'rect', // 使用方形符号，与其他形状区分
          symbolSize: 16,
          data: failurePoints,
          itemStyle: {
            color: getFailureColor(index),
            borderColor: '#FFFFFF',
            borderWidth: 3
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowColor: getFailureColor(index),
              scale: 1.4
            }
          },
          z: 15 // 确保失败标记显示在最上层
        });
      }

      // 添加均线数据系列
      const maData = [];
      if (result.daily_logs && Array.isArray(result.daily_logs)) {
        result.daily_logs.forEach(log => {
          // 从daily_logs中提取均线价格
          if (log.ma_price !== undefined && log.ma_price !== null) {
            maData.push([log.date, log.ma_price]);
          }
        });
      }

      if (maData.length > 0) {
        maSeries.push({
          name: `MA${result.period}`,
          type: 'line',
          data: maData,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: getMALineColor(index),
            opacity: 0.8
          },
          z: 5 // 确保均线在K线之上，但在标记之下
        });
      }
    }
  });

  return { dates, ohlc, buyPointsSeries, sellPointsSeries, failurePointsSeries, maSeries };
};

const renderChart = () => {
  if (!chartInstance || klineData.value.length === 0) return;
  const { dates, ohlc, buyPointsSeries, sellPointsSeries, failurePointsSeries, maSeries } = prepareChartData();

  const legendData = ['K线'];
  maSeries.forEach(series => legendData.push(series.name));
  buyPointsSeries.forEach(series => legendData.push(series.name));
  sellPointsSeries.forEach(series => legendData.push(series.name));
  failurePointsSeries.forEach(series => legendData.push(series.name));

  // 清除之前的图表配置，确保完全重新渲染
  chartInstance.clear();

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          if (param.seriesType === 'candlestick') {
            const data = param.data;
            result += `<div>开盘: ${data[1]}</div>`;
            result += `<div>收盘: ${data[2]}</div>`;
            result += `<div>最低: ${data[3]}</div>`;
            result += `<div>最高: ${data[4]}</div>`;
          } else if (param.seriesType === 'scatter') {
            if (param.seriesName.includes('投资失败')) {
              result += `<div style="color: ${param.color}; font-weight: bold;">⚠️ ${param.seriesName}: 三阶段投资失败</div>`;
            } else {
              result += `<div style="color: ${param.color};">${param.seriesName}: ${param.data[1]}</div>`;
            }
          }
        });
        return result;
      }
    },
    legend: {
      data: legendData,
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      scale: true,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: '{value}'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        start: 70,
        end: 100,
        height: 30
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: ohlc,
        itemStyle: {
          color: '#ef232a',
          color0: '#14b143',
          borderColor: '#ef232a',
          borderColor0: '#14b143'
        }
      },
      ...maSeries,
      ...buyPointsSeries,
      ...sellPointsSeries,
      ...failurePointsSeries
    ]
  };

  chartInstance.setOption(option, true); // 第二个参数为true，表示不合并配置，完全替换
};

const updateChart = () => {
  console.log('更新图表，当前可见周期:', visiblePeriods.value);
  renderChart();
};

const fetchKlineData = async () => {
  try {
    klineData.value = await getStockData(props.stockCode, props.startDate, props.endDate);
    renderChart();
  } catch (error) {
    console.error("获取K线数据失败:", error);
  }
};

// 初始化可见周期（默认显示前3个周期）
const initVisiblePeriods = () => {
  if (props.simulationResults && props.simulationResults.length > 0) {
    visiblePeriods.value = props.simulationResults
      .slice(0, Math.min(3, props.simulationResults.length))
      .map(result => result.period);
  }
};

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    initVisiblePeriods();
    fetchKlineData();
  }
});

watch(() => props.simulationResults, () => {
  initVisiblePeriods();
  fetchKlineData();
}, { deep: true });
</script>

<style scoped>
.k-line-chart-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-symbol {
  font-weight: bold;
  font-size: 14px;
}

.ma-symbol {
  color: #52C41A; /* 绿色系 */
  font-weight: bold;
  font-size: 16px;
}

.buy-symbol {
  color: #1890FF; /* 蓝色系 */
}

.sell-symbol {
  color: #FA8C16; /* 橙色系 */
}

.failure-symbol {
  color: #722ED1; /* 紫色系 */
}

.period-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.period-controls :deep(.el-checkbox) {
  margin-right: 15px;
}

.period-controls :deep(.el-checkbox__label) {
  font-size: 12px;
}
</style>